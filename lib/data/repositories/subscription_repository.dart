import 'package:cussme/data/data.dart';
import 'package:cussme/di/general_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'subscription_repository.g.dart';

abstract class SubscriptionRepository {
  Stream<PurchaseResult> get purchaseResultStream;

  Future<ProductDetails> getProductDetail(String productId);

  Future<void> purchaseProduct(PurchaseParam purchaseParam);

  Future<void> restorePurchases(String userId);

  Future<bool> isInAppPurchaseAvailable();

  Future<SubscriptionVerificationResponse?> verifyAndSubmit(
      SubscriptionVerificationRequest request);

  void dispose();
}

@riverpod
SubscriptionRepository subscriptionRepository(Ref ref) {
  final inAppPurchase = InAppPurchase.instance;
  final supabaseClient = ref.read(supabaseClientProvider);
  final repository = SubscriptionRepositoryImpl(inAppPurchase, supabaseClient);

  ref.onDispose(() {
    repository.dispose();
  });

  return repository;
}
